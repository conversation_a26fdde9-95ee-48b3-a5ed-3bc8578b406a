{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-26:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "801,918,1020,1127", "endColumns": "116,101,106,103", "endOffsets": "913,1015,1122,1226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,1231", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,1327"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-24:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "801,918,1020,1127", "endColumns": "116,101,106,103", "endOffsets": "913,1015,1122,1226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,1231", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,1327"}}]}]}