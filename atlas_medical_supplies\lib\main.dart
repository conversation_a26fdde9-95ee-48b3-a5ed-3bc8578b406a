import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'services/auth_service.dart';
import 'screens/login_screen.dart';
import 'screens/dashboard_screen.dart';
import 'widgets/atlas_logo.dart';

void main() {
  runApp(const AtlasMedicalApp());
}

class AtlasMedicalApp extends StatelessWidget {
  const AtlasMedicalApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'أطلس للمستلزمات الطبية',
      debugShowCheckedModeBanner: false,

      // دعم اللغة العربية و RTL
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', 'EG'), // العربية - مصر
      ],
      locale: const Locale('ar', 'EG'),

      // التصميم والألوان التركوازية
      theme: ThemeData(
        primarySwatch: Colors.cyan,
        primaryColor: const Color(0xFF40E0D0),
        scaffoldBackgroundColor: const Color(0xFFE0FFFF),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF40E0D0),
          primary: const Color(0xFF40E0D0),
          secondary: const Color(0xFF40E0D0),
        ),
        useMaterial3: true,

        // خط عربي واضح
        fontFamily: 'Arial',
        textTheme: const TextTheme(
          displayLarge: TextStyle(fontFamily: 'Arial'),
          displayMedium: TextStyle(fontFamily: 'Arial'),
          displaySmall: TextStyle(fontFamily: 'Arial'),
          headlineLarge: TextStyle(fontFamily: 'Arial'),
          headlineMedium: TextStyle(fontFamily: 'Arial'),
          headlineSmall: TextStyle(fontFamily: 'Arial'),
          titleLarge: TextStyle(fontFamily: 'Arial'),
          titleMedium: TextStyle(fontFamily: 'Arial'),
          titleSmall: TextStyle(fontFamily: 'Arial'),
          bodyLarge: TextStyle(fontFamily: 'Arial'),
          bodyMedium: TextStyle(fontFamily: 'Arial'),
          bodySmall: TextStyle(fontFamily: 'Arial'),
          labelLarge: TextStyle(fontFamily: 'Arial'),
          labelMedium: TextStyle(fontFamily: 'Arial'),
          labelSmall: TextStyle(fontFamily: 'Arial'),
        ),

        // تصميم AppBar
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF40E0D0),
          foregroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
        ),

        // تصميم الأزرار
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF40E0D0),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 3,
          ),
        ),

        // تصميم البطاقات
        cardTheme: CardThemeData(
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          color: Colors.white,
        ),

        // تصميم حقول الإدخال
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF40E0D0)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(0xFF40E0D0), width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
          labelStyle: const TextStyle(color: Color(0xFF40E0D0)),
        ),

        // تصميم FloatingActionButton
        floatingActionButtonTheme: const FloatingActionButtonThemeData(
          backgroundColor: Color(0xFF40E0D0),
          foregroundColor: Colors.white,
        ),
      ),

      home: const SplashScreen(),
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    // انتظار قصير لعرض شاشة البداية
    await Future.delayed(const Duration(seconds: 2));

    final isLoggedIn = await AuthService.isLoggedIn();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) =>
              isLoggedIn ? const DashboardScreen() : const LoginScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFE0FFFF),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // شعار ATLAS الاحترافي
            const AtlasLogo(
              width: 220,
              height: 130,
              fontSize: 46,
              subtitleFontSize: 13,
            ),
            const SizedBox(height: 40),

            // اسم التطبيق بالعربية
            const Text(
              'أطلس للمستلزمات الطبية',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Color(0xFF40E0D0),
                shadows: [
                  Shadow(
                    offset: Offset(1, 1),
                    blurRadius: 2,
                    color: Colors.black12,
                  ),
                ],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            const Text(
              'نظام إدارة العملاء والفواتير',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 50),

            // مؤشر التحميل مع تصميم محسن
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: const SizedBox(
                width: 30,
                height: 30,
                child: CircularProgressIndicator(
                  color: Color(0xFF40E0D0),
                  strokeWidth: 3,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
