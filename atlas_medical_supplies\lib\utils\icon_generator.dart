import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';

class IconGenerator {
  static Future<void> generateAtlasIcon() async {
    // إنشاء أيقونة ATLAS
    final recorder = ui.PictureRecorder();
    final canvas = <PERSON>vas(recorder);
    const size = Size(1024, 1024); // حجم الأيقونة القياسي

    // خلفية متدرجة
    final paint = Paint()
      ..shader = const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF40E0D0), Color(0xFF20B2AA), Color(0xFF008B8B)],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    // رسم الخلفية
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        const Radius.circular(200),
      ),
      paint,
    );

    // إضافة تأثير الخلفية
    final overlayPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Colors.white.withValues(alpha: 0.1),
          Colors.transparent,
          Colors.black.withValues(alpha: 0.1),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        const Radius.circular(200),
      ),
      overlayPaint,
    );

    // رسم كلمة ATLAS
    const textStyle = TextStyle(
      fontSize: 400,
      fontWeight: FontWeight.w900,
      color: Colors.white,
      letterSpacing: 40,
      shadows: [
        Shadow(offset: Offset(20, 20), blurRadius: 40, color: Colors.black26),
      ],
    );

    final textSpan = TextSpan(text: 'ATLAS', style: textStyle);

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // تحديد موقع النص في المنتصف
    final textOffset = Offset(
      (size.width - textPainter.width) / 2,
      (size.height - textPainter.height) / 2 - 50,
    );

    textPainter.paint(canvas, textOffset);

    // رسم خط تحت النص
    final linePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 20
      ..style = PaintingStyle.fill;

    final lineRect = Rect.fromLTWH(
      (size.width - 300) / 2,
      textOffset.dy + textPainter.height + 30,
      300,
      20,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(lineRect, const Radius.circular(10)),
      linePaint,
    );

    // رسم أيقونة طبية صغيرة
    final iconPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    final iconRect = Rect.fromLTWH(size.width - 150, 100, 100, 100);

    canvas.drawRRect(
      RRect.fromRectAndRadius(iconRect, const Radius.circular(50)),
      iconPaint,
    );

    // إنشاء الصورة
    final picture = recorder.endRecording();
    final image = await picture.toImage(1024, 1024);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final bytes = byteData!.buffer.asUint8List();

    // حفظ الصورة
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/atlas_icon.png');
    await file.writeAsBytes(bytes);

    print('تم إنشاء الأيقونة في: ${file.path}');
  }
}
